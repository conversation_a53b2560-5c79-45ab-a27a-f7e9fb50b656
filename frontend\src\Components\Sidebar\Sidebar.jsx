import React, { useState, useEffect } from "react";
import "./Sidebar.css";
import { Home, User, MessageCircle, Users, Rss, BookOpen, Clock, Menu, X } from "lucide-react";

const Sidebar = () => {
  const [isOpen, setIsOpen] = useState(true);

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  // Update CSS custom property for navbar positioning
  useEffect(() => {
    document.documentElement.style.setProperty('--sidebar-width', isOpen ? '240px' : '60px');
  }, [isOpen]);

  return (
    <>
      {/* Toggle Button */}
      <button className="sidebar-toggle" onClick={toggleSidebar}>
        {isOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {/* Sidebar */}
      <div className={`sidebar ${isOpen ? 'open' : 'closed'}`}>
        <div className="section">
          <ul className="list">
            <li>
              <Home size={18} />
              {isOpen && <span>Home</span>}
            </li>
            <li>
              <User size={18} />
              {isOpen && <span>Profile</span>}
            </li>
          </ul>
        </div>

        {isOpen && <h4 className="section-title">Favorites</h4>}

        <div className="section">
          <ul className="list">
            <li>
              <MessageCircle size={18} />
              {isOpen && <span>Messages</span>}
            </li>
            <li>
              <Users size={18} />
              {isOpen && <span>Friends</span>}
            </li>
            <li>
              <Rss size={18} />
              {isOpen && <span>Feed</span>}
            </li>
            <li>
              <BookOpen size={18} />
              {isOpen && <span>Stories</span>}
            </li>
            <li>
              <Clock size={18} />
              {isOpen && <span>Memories</span>}
            </li>
          </ul>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
