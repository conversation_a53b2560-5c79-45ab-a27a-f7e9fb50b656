.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e1e8ed;
  position: fixed;
  top: 0;
  left: 240px; /* Start after sidebar */
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar h3 {
  margin: 0;
  color: #4CAF50;
  font-size: 24px;
  font-weight: 700;
  margin-left: 0; /* No need for margin since navbar starts after sidebar */
}



.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  color: #657786;
  z-index: 1;
}

.search-input {
  padding: 10px 16px 10px 44px;
  border-radius: 25px;
  border: 1px solid #e1e8ed;
  width: 350px;
  font-size: 14px;
  background-color: #f5f7fa;
  outline: none;
  transition: all 0.2s;
}

.search-input:focus {
  border-color: #4CAF50;
  background-color: #fff;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-btn {
  position: relative;
  background: none;
  border: none;
  padding: 8px;
  border-radius: 50%;
  color: #657786;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background-color: #f5f7fa;
  color: #4CAF50;
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #e74c3c;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.profile-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.profile-section:hover {
  background-color: #f5f7fa;
}

.profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-name {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
}

@media (max-width: 768px) {
  .search-input {
    width: 200px;
  }

  .navbar-actions {
    gap: 8px;
  }

  .profile-name {
    display: none;
  }
}