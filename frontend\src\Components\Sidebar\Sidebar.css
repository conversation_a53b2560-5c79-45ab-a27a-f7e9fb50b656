.sidebar {
  width: 240px;
  height: 100vh;
  background: #2c3e50;
  border-right: 1px solid #34495e;
  display: flex;
  flex-direction: column;
  padding: 70px 0 20px 0; /* Top padding to account for fixed navbar */
  font-family: "Inter", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 998; /* Lower than navbar */
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.sidebar.closed {
  width: 60px;
}

.sidebar-toggle {
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 1001;
  background: #2c3e50;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sidebar-toggle:hover {
  background: #34495e;
  transform: scale(1.05);
}

.section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #bdc3c7;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 0 10px 25px;
}

.list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  margin: 4px 8px;
  font-size: 15px;
  color: #ecf0f1;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.sidebar.closed .list li {
  padding: 12px;
  margin: 4px;
  justify-content: center;
}

.sidebar.closed .list li span {
  display: none;
}

.list li:hover {
  background: rgba(52, 152, 219, 0.2);
  color: #d2d7da;
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.list li:hover::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: #d3d3d3;
  border-radius: 0 4px 4px 0;
}

.list li:active {
  transform: translateX(3px) scale(0.98);
}

/* Icon styling */
.list li svg {
  transition: all 0.3s ease;
}

.list li:hover svg {
  color: #252d33;
  transform: scale(1.1);
}

/* Simple top border instead of animation */
.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: #3498db;
}

/* Improve section spacing */
.section:first-child {
  margin-top: 10px;
}


