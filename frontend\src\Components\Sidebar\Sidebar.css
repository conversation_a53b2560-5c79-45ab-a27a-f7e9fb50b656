.sidebar {
  width: 240px;
  height: 100vh;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  border-right: 1px solid #34495e;
  display: flex;
  flex-direction: column;
  padding: 80px 0 20px 0; /* Top padding to account for fixed navbar */
  font-family: "Inter", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #bdc3c7;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 0 10px 25px;
}

.list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  margin: 4px 8px;
  font-size: 15px;
  color: #ecf0f1;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.list li:hover {
  background: rgba(52, 152, 219, 0.2);
  color: #3498db;
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.list li:hover::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: #3498db;
  border-radius: 0 4px 4px 0;
}

.list li:active {
  transform: translateX(3px) scale(0.98);
}

/* Icon styling */
.list li svg {
  transition: all 0.3s ease;
}

.list li:hover svg {
  color: #3498db;
  transform: scale(1.1);
}

/* Add subtle animation for the entire sidebar */
.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3498db, #2980b9, #3498db);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Improve section spacing */
.section:first-child {
  margin-top: 10px;
}


